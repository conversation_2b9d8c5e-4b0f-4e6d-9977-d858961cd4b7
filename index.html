<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>WD POINTCLOUD Viewer</title>

	<link rel="stylesheet" type="text/css" href="./libs/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="./libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="./libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="./libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="./libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="./libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="./libs/spectrum/spectrum.js"></script>
	<script src="./libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="./libs/other/BinaryHeap.js"></script>
	<script src="./libs/tween/tween.min.js"></script>
	<script src="./libs/d3/d3.js"></script>
	<script src="./libs/proj4/proj4.js"></script>
	<script src="./libs/openlayers3/ol.js"></script>
	<script src="./libs/i18next/i18next.js"></script>
	<script src="./libs/jstree/jstree.js"></script>
	<script src="./libs/potree/potree.js"></script>
	<script src="./libs/plasio/js/laslaz.js"></script>
	
	<!-- add library for shapefile -->
	<script src="./libs/shapefile/shapefile.js"></script>
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">
		import * as THREE from "../libs/three.js/build/three.module.js";
		import {PLYLoader} from "../libs/three.js/loaders/PLYLoader.js";
		import {OBJLoader} from "../libs/three.js/loaders/OBJLoader.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(2_000_000);
		
		viewer.loadSettingsFromURL();
		
		viewer.setDescription("");
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			$("#menu_tools").next().show();
			$("#menu_clipping").next().show();
			$("#menu_scene").next().show();
			viewer.toggleSidebar();
		});
		
		
		Potree.loadPointCloud("./pointclouds/index/metadata.json", "Point Clouds", async e => { // async because we load shapefile here too.
			let scene = viewer.scene;
			let pointcloud = e.pointcloud;
			
			let material = pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.shape = Potree.PointShape.SQUARE;
			material.activeAttributeName = "rgba";
				
			scene.addPointCloud(pointcloud);
			
			viewer.fitToScreen();
			

			// visible false
			pointcloud.visible = false;

		});

		// add model wirg three js file obj and texture
		{
			let manager = new THREE.LoadingManager();
			manager.onProgress = function ( item, loaded, total ) {
				console.log( item, loaded, total );
			};
			let textureLoader = new THREE.TextureLoader( manager );
			let texture = textureLoader.load(`${Potree.resourcePath}/textures/brick_pavement.jpg`);
			let onProgress = function ( xhr ) {
				if ( xhr.lengthComputable ) {
					let percentComplete = xhr.loaded / xhr.total * 100;
					console.log( Math.round(percentComplete, 2) + '% downloaded' );
				}
			};
			texture.wrapS = THREE.RepeatWrapping;
			texture.wrapT = THREE.RepeatWrapping;

			let onError = function ( xhr ) {};
			let loader = new OBJLoader( manager );
			loader.load(`${Potree.resourcePath}/models/stanford_bunny_reduced.obj`, function ( object ) {
				object.traverse( function ( child ) {
					if ( child instanceof THREE.Mesh ) {
						child.material.map = texture;
					}
				} );

				object.position.set(589871.587, 231528.213, 725.634);
				object.scale.multiplyScalar(500);
				object.rotation.set(Math.PI / 2, Math.PI, 0)

				viewer.scene.scene.add( object );

				viewer.onGUILoaded(() => {
					// Add entries to object list in sidebar
					let tree = $(`#jstree_scene`);
					let parentNode = "other";

					let bunnyID = tree.jstree('create_node', parentNode, { 
							text: "Bunny Textured", 
							icon: `${Potree.resourcePath}/icons/triangle.svg`,
							data: object
						}, 
						"last", false, false);
					tree.jstree(object.visible ? "check_node" : "uncheck_node", bunnyID);

					//tree.jstree("open_node", parentNode);
				});

			}, onProgress, onError );
		}


		{ // MEASUREMENTS
			let scene = viewer.scene;
			{// DISTANCE MEASURE
				let measure = new Potree.Measure();
				measure.name = "Distance";
				measure.closed = false;
				measure.addMarker(new THREE.Vector3(664947.062, 1550580.972, 13.850));
				measure.addMarker(new THREE.Vector3(664945.530, 1550608.803, 13.538));		
				scene.addMeasurement(measure);
			}
			{// DISTANCE MEASURE
				let measure = new Potree.Measure();
				measure.name = "Distance";
				measure.closed = false;
				measure.addMarker(new THREE.Vector3(664975.784, 1550610.239, 13.513));
				measure.addMarker(new THREE.Vector3(664977.580, 1550581.783, 13.850));		
				scene.addMeasurement(measure);
			}
			{// DISTANCE MEASURE
				let measure = new Potree.Measure();
				measure.name = "Distance";
				measure.closed = false;
				measure.addMarker(new THREE.Vector3(664945.449, 1550610.441, 5.680));
				measure.addMarker(new THREE.Vector3(664947.192, 1550578.975, 5.448));		
				scene.addMeasurement(measure);
			}



			{ // AREA MEASURE
				let measure = new Potree.Measure();
				measure.name = "โดม";
				measure.closed = true;
				measure.showArea = true;
				// not show
				measure.visible = true;
				// change color
				measure.color = new THREE.Color(0x00ff00);
				// fill color
				measure.fillColor = new THREE.Color(0x00ff00);

				measure.addMarker(new THREE.Vector3(664939.8362,1550609.0952, 12.910));
				measure.addMarker(new THREE.Vector3(664940.5753,1550581.105, 12.910));
				measure.addMarker(new THREE.Vector3(664983.5603,1550582.2401, 12.910));
				measure.addMarker(new THREE.Vector3(664983.5339,1550583.2397, 12.910));
				measure.addMarker(new THREE.Vector3(664987.0327,1550583.3321, 12.910));
				measure.addMarker(new THREE.Vector3(664986.8611,1550589.8299, 12.910));
				measure.addMarker(new THREE.Vector3(664983.3624,1550589.7375, 12.910));
				measure.addMarker(new THREE.Vector3(664982.8212,1550610.2303, 12.910));
				measure.addMarker(new THREE.Vector3(664950.6435,1550609.3806, 12.910));
				
				scene.addMeasurement(measure);
			}



			{ // PROFILE
				let profile = new Potree.Profile();
				profile.setWidth(2)
				profile.addMarker(new THREE.Vector3(664959.323, 1550619.388, 15.144)); 
				profile.addMarker(new THREE.Vector3(664961.911, 1550574.456, 15.689)); 
				
				scene.addProfile(profile);
			}




			{ // HEIGHT MEASURE
				let measure = new Potree.Measure();
				measure.name = "Height";
				measure.closed = false;
				measure.showDistances = false;
				measure.showHeight = true;
				measure.addMarker(new THREE.Vector3(664945.530, 1550608.803, 13.538));
				measure.addMarker(new THREE.Vector3(664945.589, 1550608.918, 3.091));	
				scene.addMeasurement(measure);
			}
			{ // HEIGHT MEASURE
				let measure = new Potree.Measure();
				measure.name = "Height";
				measure.closed = false;
				measure.showDistances = false;
				measure.showHeight = true;
				measure.addMarker(new THREE.Vector3(664947.062, 1550580.972, 13.850));
				measure.addMarker(new THREE.Vector3(664947.584, 1550580.557, 3.187));	
				scene.addMeasurement(measure);
			}
			{ // HEIGHT MEASURE
				let measure = new Potree.Measure();
				measure.name = "Height";
				measure.closed = false;
				measure.showDistances = false;
				measure.showHeight = true;
				measure.addMarker(new THREE.Vector3(664984.453, 1550604.787, 13.815));
				measure.addMarker(new THREE.Vector3(664984.170, 1550604.796, 3.091));	
				scene.addMeasurement(measure);
			}
			{ // HEIGHT MEASURE
				let measure = new Potree.Measure();
				measure.name = "Height";
				measure.closed = false;
				measure.showDistances = false;
				measure.showHeight = true;
				measure.addMarker(new THREE.Vector3(664973.645, 1550610.219, 3.087));
				measure.addMarker(new THREE.Vector3(664973.644, 1550610.148, 13.457));	
				scene.addMeasurement(measure);
			}
			{ // HEIGHT MEASURE
				let measure = new Potree.Measure();
				measure.name = "Height";
				measure.closed = false;
				measure.showDistances = false;
				measure.showHeight = true;
				measure.addMarker(new THREE.Vector3(664980.502, 1550581.854, 13.862));
				measure.addMarker(new THREE.Vector3(664980.506, 1550581.737, 3.237));	
				scene.addMeasurement(measure);
			}
		}

		// toolbar open scene
		$("#menu_scene").next().show();
		
	

		
		
	</script>
	
	
  </body>
</html>
   